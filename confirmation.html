<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Submitted - Access Welfare Trust</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .confirmation-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f9f9f9;
            border-radius: 8px;
            text-align: center;
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .confirmation-icon {
            font-size: 5rem;
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        .confirmation-title {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .confirmation-message {
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .application-details {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: left;
        }

        .application-details h3 {
            margin-bottom: 1rem;
            color: var(--accent-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }

        .detail-item {
            margin-bottom: 0.5rem;
        }

        .detail-label {
            font-weight: bold;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn-secondary {
            background-color: var(--accent-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary:hover {
            background-color: #594c5d;
        }

        .print-instructions {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="eligibility.html">Eligibility</a>
                    <a href="application.html">Apply</a>
                </nav>
                <a href="login-signup.html" class="admin-link">Login / Sign Up</a>
            </div>
        </div>
    </header>

    <main>
        <div class="confirmation-container">
            <div class="confirmation-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2 class="confirmation-title">Application Submitted Successfully!</h2>
            <p class="confirmation-message">Thank you for submitting your scholarship application. Your application has been received and will be reviewed by our team.</p>

            <div class="application-details">
                <h3>Application Details</h3>
                <div class="detail-item">
                    <span class="detail-label">Application ID:</span>
                    <span class="detail-value" id="applicationId">AWT-2025-<script>document.write(Math.floor(Math.random() * 10000).toString().padStart(4, '0'))</script></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Submission Date:</span>
                    <span class="detail-value" id="submissionDate"><script>document.write(new Date().toLocaleDateString('en-IN'))</script></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Applicant Name:</span>
                    <span class="detail-value" id="applicantName">-</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Course Applied For:</span>
                    <span class="detail-value" id="courseApplied">-</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Scholarship Amount Requested:</span>
                    <span class="detail-value" id="scholarshipAmount">-</span>
                </div>
            </div>

            <div class="next-steps">
                <h3>Next Steps</h3>
                <ol style="text-align: left; display: inline-block;">
                    <li>Print this confirmation page for your records.</li>
                    <li>Our team will review your application and supporting documents.</li>
                    <li>You will be notified of the decision via email or phone within 4-6 weeks.</li>
                    <li>If additional information is required, we will contact you.</li>
                </ol>
            </div>

            <div class="print-instructions">
                <p><strong>Important:</strong> Please print this page or save the Application ID for future reference. You will need this ID for any inquiries regarding your application.</p>
            </div>

            <div class="action-buttons">
                <button class="btn-primary" onclick="window.print()">Print Confirmation</button>
                <a href="index.html" class="btn-secondary">Return to Homepage</a>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Access Welfare Trust is dedicated to supporting education for persons with disabilities and those from economically weaker sections.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook-f"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="eligibility.html">Eligibility</a></li>
                    <li><a href="application.html">Apply Now</a></li>
                    <li><a href="current-projects.html">Current Projects</a></li>
                    <li><a href="accomplished-projects.html">Accomplished Projects</a></li>
                    <li><a href="gallery.html">Gallery</a></li>
                    <li><a href="get-involved.html">Get Involved</a></li>
                    <li><a href="login-signup.html">Login / Sign Up</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Contact Us</h3>
                <p><i class="fas fa-map-marker-alt"></i> 1/3, Vasanth Nagar, Behind Jeya Nagar, Karumandapam, Tiruchirappalli - 620001</p>
                <p><i class="fas fa-phone"></i> +91 9498980331</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-globe"></i> www.awt.org.in</p>
            </div>
        </div>

        <div class="copyright">
            <p>&copy; 2025 Access Welfare Trust. All rights reserved. | Registration No: 18/Trichy [1]/BK-4/06/2021</p>
        </div>
    </footer>

    <script>
        // Try to populate applicant details from localStorage if available
        document.addEventListener('DOMContentLoaded', function() {
            const savedData = localStorage.getItem('scholarshipFormData');
            if (savedData) {
                const formData = JSON.parse(savedData);

                // Populate confirmation details
                if (formData.name) {
                    document.getElementById('applicantName').textContent = formData.name;
                }

                if (formData.currentCourse) {
                    document.getElementById('courseApplied').textContent = formData.currentCourse;
                }

                if (formData.amountFigures) {
                    document.getElementById('scholarshipAmount').textContent = '₹' + formData.amountFigures;
                }
            }
        });
    </script>
</body>
</html>
