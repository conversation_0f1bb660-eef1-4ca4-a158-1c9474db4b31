/* Global Styles */
:root {
    --primary-color: #1e5f74;
    --secondary-color: #e63946;
    --accent-color: #457b9d;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-color: #dee2e6;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --gradient-primary: linear-gradient(135deg, #1e5f74 0%, #457b9d 100%);
    --gradient-secondary: linear-gradient(135deg, #e63946 0%, #f1948a 100%);
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    background-image: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    min-height: 100vh;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

/* Header Styles */
header {
    background-color: white;
    padding: 0.5rem 2rem;
    box-shadow: var(--box-shadow);
    position: relative;
    animation: slideInDown 0.8s ease-out;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo {
    width: 170px;
    height: auto;
}

header h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
    animation: slideInLeft 0.8s ease-out 0.3s both;
}

.tagline {
    color: var(--accent-color);
    font-style: italic;
    font-size: 0.9rem;
    margin-top: 0.2rem;
    animation: slideInLeft 0.8s ease-out 0.5s both;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.nav-links a {
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: var(--transition);
    position: relative;
    animation: slideInRight 0.6s ease-out calc(0.1s * var(--i, 0)) both;
}

.nav-links a:hover,
.nav-links a.active {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.admin-link {
    background-color: var(--primary-color);
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    transition: var(--transition);
    animation: slideInRight 0.8s ease-out 0.8s both;
    position: relative;
    overflow: hidden;
}

.admin-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease-out;
}

.admin-link:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(30, 95, 116, 0.4);
}

.admin-link:hover::before {
    left: 100%;
}

/* Main Content Styles */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

section {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    animation: slideInUp 0.8s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

section:nth-child(1) { animation-delay: 0.1s; }
section:nth-child(2) { animation-delay: 0.2s; }
section:nth-child(3) { animation-delay: 0.3s; }
section:nth-child(4) { animation-delay: 0.4s; }
section:nth-child(5) { animation-delay: 0.5s; }

section:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
    font-weight: 600;
    animation: slideInLeft 0.8s ease-out;
}

h2:hover {
    animation: wiggle 1s ease-out;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background-color: var(--secondary-color);
}

h3 {
    color: var(--accent-color);
    margin-bottom: 1.2rem;
    font-weight: 600;
    animation: slideInRight 0.8s ease-out 0.2s both;
}

h3:hover {
    animation: swing 1s ease-out;
}

/* Hero Section */
.hero {
    background-image: var(--gradient-primary);
    padding: 4rem 2rem;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
    animation: zoomIn 1s ease-out;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero h2 {
    color: white;
    border-bottom: none;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    animation: slideInDown 1s ease-out 0.3s both;
}

.hero h2::after {
    display: none;
}

.hero h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    font-weight: 500;
    animation: slideInUp 1s ease-out 0.5s both;
}

.deadline {
    margin: 1.5rem auto;
    padding: 0.8rem 1.5rem;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 50px;
    font-weight: bold;
    display: inline-block;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    max-width: 400px;
    animation: scaleIn 1s ease-out 0.7s both;
}

.deadline:hover {
    animation: pulse 1s infinite;
}

.btn-primary {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    margin-top: 1.5rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(230, 57, 70, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: bounce 2s ease-out 1s both;
}

.btn-primary:hover {
    background-color: #d62839;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(230, 57, 70, 0.4);
    animation: pulse 0.6s infinite;
}

/* About Section */
.about p {
    margin-bottom: 1.2rem;
    line-height: 1.8;
    color: #495057;
    animation: fadeIn 0.8s ease-out;
}

.about p:hover {
    animation: rubberBand 1s ease-out;
}

.contact-info {
    background-color: #f8f9fa;
    padding: 1.8rem;
    border-radius: 8px;
    margin-top: 2rem;
    border-left: 4px solid var(--primary-color);
    animation: slideInFromBottom 0.8s ease-out 0.5s both;
}

.contact-info:hover {
    animation: jello 1s ease-out;
}

.contact-info p {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info p i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 24px;
    animation: heartbeat 2s ease-in-out infinite;
}

/* Eligibility Section */
.eligibility ul {
    list-style: none;
    margin-left: 0;
    padding-left: 0;
}

.eligibility li {
    margin-bottom: 1rem;
    padding-left: 2rem;
    position: relative;
    line-height: 1.8;
    animation: slideInLeft 0.6s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

.eligibility li:nth-child(1) { animation-delay: 0.1s; }
.eligibility li:nth-child(2) { animation-delay: 0.2s; }
.eligibility li:nth-child(3) { animation-delay: 0.3s; }
.eligibility li:nth-child(4) { animation-delay: 0.4s; }
.eligibility li:nth-child(5) { animation-delay: 0.5s; }

.eligibility li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    animation: bounce 2s ease-in-out infinite;
}

/* Page Header */
.page-header {
    background-color: white;
    padding: 2rem;
    text-align: center;
    color: #457b9d;
    margin-bottom: 2rem;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.page-header::before {
    display: none;
}

.page-header-content {
    position: relative;
    z-index: 1;
}

.page-header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.8rem;
    color: #457b9d;
    font-weight: 600;
    border-bottom: none;
    animation: slideInFromTop 1s ease-out;
}

.page-header h1:hover {
    animation: rubberBand 1s ease-out;
}

.page-header p {
    font-size: 1rem;
    opacity: 0.9;
}

/* About Page Styles */
.about-section,
.eligibility-section {
    padding: 0;
}

.about-content,
.eligibility-content {
    max-width: 900px;
    margin: 0 auto;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.value-card {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
}

.value-card:hover {
    transform: translateY(-10px);
    border-bottom: 3px solid var(--primary-color);
    box-shadow: var(--box-shadow);
}

.value-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.contact-card {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: var(--transition);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.contact-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.registration-info {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 10px;
}

/* Eligibility Page Styles */
.criteria-card {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 10px;
    transition: var(--transition);
}

.criteria-card:hover {
    transform: translateX(10px);
    box-shadow: var(--box-shadow);
}

.criteria-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-right: 1.5rem;
    min-width: 60px;
    text-align: center;
}

.criteria-details h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.documents-list ul {
    list-style: none;
    padding-left: 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.documents-list li {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.documents-list li:hover {
    transform: translateX(5px);
    background-color: rgba(30, 95, 116, 0.1);
}

.documents-list i {
    color: var(--primary-color);
    margin-right: 0.8rem;
}

.application-cta {
    margin-top: 3rem;
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 10px;
    border-top: 4px solid var(--primary-color);
}

.deadline-note {
    margin-top: 1rem;
    color: var(--secondary-color);
    font-weight: 500;
}

/* Form Styles */
.application-container {
    max-width: 900px;
    margin: 0 auto;
    background-color: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: var(--box-shadow);
}

.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3rem;
    position: relative;
    padding-bottom: 1rem;
}

.form-progress::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--border-color);
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.8rem;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.step.active .step-number {
    background-image: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 4px 10px rgba(30, 95, 116, 0.3);
}

.step-title {
    font-size: 0.9rem;
    text-align: center;
    font-weight: 500;
    color: var(--dark-color);
    transition: var(--transition);
}

.step.active .step-title {
    color: var(--primary-color);
    font-weight: 600;
}

.form-step {
    display: none;
    animation: fadeIn 0.5s ease;
}

/* Enhanced Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInDown {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(30, 95, 116, 0.3); }
    50% { box-shadow: 0 0 20px rgba(30, 95, 116, 0.6); }
    100% { box-shadow: 0 0 5px rgba(30, 95, 116, 0.3); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes rotateIn {
    from { opacity: 0; transform: rotate(-180deg) scale(0.5); }
    to { opacity: 1; transform: rotate(0deg) scale(1); }
}

@keyframes flipIn {
    from { opacity: 0; transform: rotateY(-90deg); }
    to { opacity: 1; transform: rotateY(0deg); }
}

@keyframes zoomIn {
    from { opacity: 0; transform: scale(0.3); }
    to { opacity: 1; transform: scale(1); }
}

/* Scroll-triggered animation classes */
.animate-in {
    animation: slideInUp 0.8s ease-out forwards !important;
}

.animate-in.from-left {
    animation: slideInLeft 0.8s ease-out forwards !important;
}

.animate-in.from-right {
    animation: slideInRight 0.8s ease-out forwards !important;
}

.animate-in.scale {
    animation: scaleIn 0.8s ease-out forwards !important;
}

.animate-in.rotate {
    animation: rotateIn 0.8s ease-out forwards !important;
}

.animate-in.flip {
    animation: flipIn 0.8s ease-out forwards !important;
}

/* Loading animations */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Hover effects */
.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(30, 95, 116, 0.5);
}

/* Text animations */
.text-reveal {
    overflow: hidden;
    position: relative;
}

.text-reveal::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    animation: textReveal 1.5s ease-out forwards;
}

@keyframes textReveal {
    0% { left: 0; }
    50% { left: 0; }
    100% { left: 100%; }
}

@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(4); opacity: 0; }
}

@keyframes slideInFromBottom {
    from { opacity: 0; transform: translateY(100px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInFromTop {
    from { opacity: 0; transform: translateY(-100px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInScale {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes wiggle {
    0%, 7% { transform: rotateZ(0); }
    15% { transform: rotateZ(-15deg); }
    20% { transform: rotateZ(10deg); }
    25% { transform: rotateZ(-10deg); }
    30% { transform: rotateZ(6deg); }
    35% { transform: rotateZ(-4deg); }
    40%, 100% { transform: rotateZ(0); }
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

@keyframes swing {
    20% { transform: rotate3d(0, 0, 1, 15deg); }
    40% { transform: rotate3d(0, 0, 1, -10deg); }
    60% { transform: rotate3d(0, 0, 1, 5deg); }
    80% { transform: rotate3d(0, 0, 1, -5deg); }
    100% { transform: rotate3d(0, 0, 1, 0deg); }
}

@keyframes rubberBand {
    0% { transform: scale3d(1, 1, 1); }
    30% { transform: scale3d(1.25, 0.75, 1); }
    40% { transform: scale3d(0.75, 1.25, 1); }
    50% { transform: scale3d(1.15, 0.85, 1); }
    65% { transform: scale3d(0.95, 1.05, 1); }
    75% { transform: scale3d(1.05, 0.95, 1); }
    100% { transform: scale3d(1, 1, 1); }
}

@keyframes jello {
    0%, 11.1%, 100% { transform: translate3d(0, 0, 0); }
    22.2% { transform: skewX(-12.5deg) skewY(-12.5deg); }
    33.3% { transform: skewX(6.25deg) skewY(6.25deg); }
    44.4% { transform: skewX(-3.125deg) skewY(-3.125deg); }
    55.5% { transform: skewX(1.5625deg) skewY(1.5625deg); }
    66.6% { transform: skewX(-0.78125deg) skewY(-0.78125deg); }
    77.7% { transform: skewX(0.390625deg) skewY(0.390625deg); }
    88.8% { transform: skewX(-0.1953125deg) skewY(-0.1953125deg); }
}

.form-step:first-child {
    display: block;
}

.form-step h3 {
    margin-bottom: 2rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 2rem;
}

label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 500;
    color: var(--dark-color);
}

.inline-label {
    display: inline-block;
    margin: 0 0.8rem;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="url"],
select,
textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background-color: #f8f9fa;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 95, 116, 0.1);
    background-color: white;
}

.address-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 1rem;
}

.photo-upload {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 2.5rem;
}

.photo-placeholder {
    width: 150px;
    height: 180px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    transition: var(--transition);
}

.photo-placeholder:hover {
    border-color: var(--primary-color);
    background-color: rgba(30, 95, 116, 0.05);
}

.photo-placeholder p {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

input[type="file"] {
    display: none;
}

label[for="photo"],
.file-upload label {
    background-image: var(--gradient-primary);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-block;
    margin-top: 0.8rem;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(30, 95, 116, 0.2);
}

label[for="photo"]:hover,
.file-upload label:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(30, 95, 116, 0.3);
}

.form-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.btn-prev,
.btn-next,
.btn-submit {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-prev {
    background-color: #f8f9fa;
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

.btn-next,
.btn-submit {
    background-image: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 10px rgba(30, 95, 116, 0.2);
}

.btn-submit {
    background-image: var(--gradient-secondary);
    box-shadow: 0 4px 10px rgba(230, 57, 70, 0.2);
}

.btn-prev:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
}

.btn-next:hover,
.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(30, 95, 116, 0.3);
}

.btn-submit:hover {
    box-shadow: 0 6px 15px rgba(230, 57, 70, 0.3);
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 1rem;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

th, td {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
}

th {
    background-color: #f5f5f5;
    font-weight: 600;
    text-align: left;
}

table input,
table select {
    padding: 0.5rem;
    width: 100%;
}

/* Section Styles */
.section-heading {
    font-weight: bold;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.certificate-details,
.fee-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .certificate-details {
        grid-template-columns: 1fr 1fr 1fr;
    }

    .fee-details {
        grid-template-columns: 1fr 1fr;
    }
}

.file-upload {
    margin-top: 1rem;
}

.radio-group {
    display: flex;
    gap: 2rem;
    margin-top: 0.5rem;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    margin-bottom: 0;
}

.declaration {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.signature-fields {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
}

.document-note {
    margin-bottom: 1rem;
    font-style: italic;
}

.document-uploads {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 768px) {
    .document-uploads {
        grid-template-columns: 1fr 1fr;
    }
}

.document-item {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
}

.checkbox-group {
    margin-top: 1rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* Footer Styles */
footer {
    text-align: center;
    padding: 3rem 2rem;
    background-color: var(--dark-color);
    color: white;
    margin-top: 4rem;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background-image: var(--gradient-primary);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
    }
}

.footer-section h3 {
    color: white;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
    padding-left: 5px;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.copyright {
    margin-top: 3rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Quick Links */
.quick-links {
    margin-top: 4rem;
}

.quick-links-grid {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: nowrap;
}

.quick-link-card {
    background-color: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    color: var(--dark-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 3px solid transparent;
    flex: 1;
    min-width: 0; /* Prevents flex items from overflowing */
    animation: slideInUp 0.8s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

.quick-link-card:nth-child(1) { animation-delay: 0.2s; }
.quick-link-card:nth-child(2) { animation-delay: 0.4s; }
.quick-link-card:nth-child(3) { animation-delay: 0.6s; }
.quick-link-card:nth-child(4) { animation-delay: 0.8s; }

.quick-link-card:hover {
    transform: translateY(-10px);
    border-bottom: 3px solid var(--primary-color);
    color: var(--primary-color);
    animation: float 2s ease-in-out infinite;
}

.quick-link-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.quick-link-card:hover i {
    animation: rotateIn 0.6s ease-out;
}

.quick-link-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
    transition: var(--transition);
}

.quick-link-card:hover h3 {
    color: var(--primary-color);
}

.quick-link-card p {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Image Gallery */
.gallery-section {
    margin-top: 4rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 300px;
    animation: scaleIn 0.8s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

.gallery-item:nth-child(1) { animation-delay: 0.1s; }
.gallery-item:nth-child(2) { animation-delay: 0.3s; }
.gallery-item:nth-child(3) { animation-delay: 0.5s; }

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: none;
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1.5rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.3));
    color: white;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.4s ease-out;
}

.gallery-caption h3 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.gallery-caption p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Projects Styles */
.ongoing-projects {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.project-item {
    display: flex;
    margin-bottom: 4rem;
    align-items: center;
    flex-wrap: wrap;
}

.project-content-right {
    flex: 1;
    padding: 0 2rem;
    order: 1;
}

.project-content-left {
    flex: 1;
    padding: 0 2rem;
    order: 2;
}

.project-image-left {
    flex: 1;
    order: 2;
    min-width: 300px;
}

.project-image-right {
    flex: 1;
    order: 1;
    min-width: 300px;
}

.project-item h2 {
    color: #457b9d;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: none;
    padding-bottom: 0;
    font-weight: 600;
}

.project-item h2::after {
    display: none;
}

.project-item p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
    color: #666;
}

.project-item img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .project-item {
        flex-direction: column;
    }

    .project-content-right,
    .project-content-left {
        order: 1;
        width: 100%;
        padding: 0;
        margin-bottom: 2rem;
    }

    .project-image-left,
    .project-image-right {
        order: 2;
        width: 100%;
    }
}

.btn-secondary {
    display: inline-block;
    background-color: #f8f9fa;
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    border: 1px solid var(--primary-color);
    transition: var(--transition);
    margin-right: 1rem;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.cta-section {
    background-color: white;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    margin-top: 3rem;
}

.cta-section h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    border-bottom: none;
}

.cta-section p {
    max-width: 700px;
    margin: 0 auto 2rem;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Timeline Styles */
.timeline-section {
    max-width: 900px;
    margin: 0 auto;
}

.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50px;
    height: 100%;
    width: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    padding-left: 100px;
    margin-bottom: 3rem;
}

.timeline-dot {
    position: absolute;
    left: 47px;
    top: 0;
    width: 10px;
    height: 10px;
    background-color: var(--primary-color);
    border-radius: 50%;
    transform: translateX(-3px);
}

.timeline-date {
    position: absolute;
    left: 0;
    top: 0;
    width: 40px;
    text-align: right;
    font-weight: 700;
    color: var(--primary-color);
}

.timeline-content {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.impact-section {
    background-color: white;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    margin-top: 3rem;
}

.impact-section h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    border-bottom: none;
}

.impact-stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
}

.impact-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.impact-label {
    color: #6c757d;
    font-size: 1rem;
}

/* Get Involved Styles */
.get-involved-description {
    max-width: 900px;
    margin: 0 auto 3rem;
    padding: 0 1rem;
}

.get-involved-description h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.get-involved-description p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.get-involved-description ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.get-involved-description li {
    margin-bottom: 0.8rem;
    position: relative;
    line-height: 1.6;
}

/* Contact AWT Section Styles */
.contact-awt-section {
    max-width: 1000px;
    margin: 3rem auto;
    background-color: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.contact-awt-container {
    display: flex;
    flex-wrap: wrap;
}

.contact-awt-info {
    flex: 1;
    background-color: #0e6e6e;
    color: white;
    padding: 3rem 2rem;
    min-width: 300px;
}

.contact-awt-info h2 {
    color: white;
    margin-bottom: 2rem;
    font-size: 2rem;
    border-bottom: none;
}

.contact-awt-info p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-address {
    margin-top: 2rem;
}

.contact-email, .contact-phone {
    font-weight: 500;
}

.contact-message {
    margin-top: 2rem;
    font-size: 0.95rem;
    line-height: 1.8;
}

.contact-awt-form {
    flex: 1;
    padding: 3rem 2rem;
    background-color: #e9ecef;
    min-width: 300px;
}

.form-field {
    margin-bottom: 1.5rem;
}

.form-field-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-field-row .form-field {
    flex: 1;
    margin-bottom: 0;
}

.contact-awt-form input,
.contact-awt-form textarea {
    width: 100%;
    padding: 0.5rem 0;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #6c757d;
    font-size: 1rem;
    color: #333;
}

.contact-awt-form input::placeholder,
.contact-awt-form textarea::placeholder {
    color: #6c757d;
}

.contact-awt-form input:focus,
.contact-awt-form textarea:focus {
    outline: none;
    border-bottom: 2px solid var(--primary-color);
}

.contact-awt-form textarea {
    min-height: 100px;
    resize: vertical;
}

.form-submit {
    text-align: right;
}

.submit-btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: transparent;
    color: #333;
    border: 1px solid #6c757d;
    border-radius: 50px;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.submit-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.involvement-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 3rem;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    padding: 2rem;
}

.involvement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.involvement-icon {
    font-size: 4rem;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1.5rem;
}

.involvement-content h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
    border-bottom: none;
}

.involvement-content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.involvement-content h3 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.involvement-content ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.involvement-content li {
    margin-bottom: 0.5rem;
    position: relative;
}

.involvement-content li::before {
    content: '✓';
    position: absolute;
    left: -1.5rem;
    color: var(--primary-color);
}

.involvement-form,
.donation-options,
.partnership-contact {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1.5rem;
}

.donation-amounts {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.donation-amount {
    padding: 0.8rem 1.5rem;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
}

.donation-amount:hover,
.donation-amount.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.donation-note {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
}

.testimonials {
    background-color: white;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    margin-top: 3rem;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.testimonials h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    border-bottom: none;
}

.testimonial-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding-bottom: 1rem;
}

.testimonial {
    flex: 0 0 calc(50% - 1rem);
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: left;
}

.testimonial-content {
    margin-bottom: 1.5rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h4 {
    margin: 0;
    color: var(--primary-color);
}

.author-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    main {
        padding: 1.5rem;
    }

    .header-container {
        padding: 0 1rem;
    }
}

@media (max-width: 992px) {
    .project-card {
        flex-direction: column;
    }

    .project-image {
        height: 250px;
    }

    .testimonial-slider {
        flex-direction: column;
    }

    .testimonial {
        flex: 0 0 100%;
    }

    .form-field-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .quick-links-grid {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .quick-link-card {
        flex: 1 1 calc(50% - 0.5rem);
        margin-bottom: 1.5rem;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .hero {
        padding: 3rem 1.5rem;
    }

    .contact-awt-container {
        flex-direction: column;
    }

    .contact-awt-info,
    .contact-awt-form {
        min-width: 100%;
    }

    .values-grid,
    .contact-grid {
        grid-template-columns: 1fr;
    }

    .donation-amounts {
        justify-content: center;
    }

    .involvement-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    /* Header adjustments */
    header {
        padding: 0.5rem 1rem;
    }

    .header-container {
        flex-direction: column;
        gap: 1rem;
    }

    .logo-container {
        justify-content: center;
        text-align: center;
    }

    .logo {
        width: 120px;
    }

    header h1 {
        font-size: 1.3rem;
    }

    .tagline {
        font-size: 0.8rem;
    }

    .header-right {
        flex-direction: column;
        width: 100%;
    }

    .nav-links {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        width: 100%;
        margin-bottom: 1rem;
        gap: 0.5rem;
    }

    .nav-links a {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    .admin-link {
        width: 100%;
        text-align: center;
        max-width: 200px;
        margin: 0 auto;
    }

    /* Main content adjustments */
    main {
        padding: 1rem;
    }

    section {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    /* Hero section */
    .hero {
        padding: 2rem 1rem;
    }

    .hero h2 {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
    }

    .hero h3 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }

    /* Page header mobile adjustments */
    .page-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .page-header h1 {
        font-size: 1.3rem;
        margin-bottom: 0.6rem;
    }

    .page-header p {
        font-size: 0.9rem;
    }

    /* Section mobile adjustments */
    section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .deadline {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .btn-primary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Quick links */
    .quick-links-grid {
        flex-direction: column;
        gap: 1rem;
    }

    .quick-link-card {
        flex: 1 1 100%;
        margin-bottom: 1rem;
        padding: 1.5rem;
    }

    .quick-link-card i {
        font-size: 2.5rem;
    }

    .quick-link-card h3 {
        font-size: 1.1rem;
    }

    /* Gallery grid */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .gallery-item {
        height: 250px;
    }

    /* Form styles */
    .form-progress {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }

    .form-progress::after {
        display: none;
    }

    .step {
        width: 30%;
        margin-bottom: 1rem;
        min-width: 80px;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .step-title {
        font-size: 0.75rem;
    }

    .address-fields {
        grid-template-columns: 1fr;
    }

    .signature-fields {
        grid-template-columns: 1fr;
    }

    .document-uploads {
        grid-template-columns: 1fr;
    }

    /* Project styles */
    .project-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .project-stats {
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    /* Timeline adjustments */
    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        padding-left: 50px;
    }

    .timeline-dot {
        left: 17px;
    }

    .timeline-date {
        width: auto;
        left: 40px;
        top: -30px;
    }

    /* Impact stats */
    .impact-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .impact-number {
        font-size: 2.5rem;
    }

    /* Footer adjustments */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .social-links {
        justify-content: center;
    }

    .copyright {
        font-size: 0.8rem;
        padding: 1rem;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    /* Header */
    .logo {
        width: 100px;
    }

    header h1 {
        font-size: 1.1rem;
    }

    .tagline {
        font-size: 0.75rem;
    }

    .nav-links a {
        padding: 0.3rem 0.6rem;
        font-size: 0.85rem;
    }

    /* Hero section */
    .hero {
        padding: 1.5rem 0.8rem;
    }

    .hero h2 {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    .hero h3 {
        font-size: 1rem;
    }

    .deadline {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }

    .btn-primary {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    /* Main content */
    main {
        padding: 0.8rem;
    }

    section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    h2 {
        font-size: 1.3rem;
    }

    h3 {
        font-size: 1.1rem;
    }

    /* Quick links */
    .quick-link-card {
        padding: 1.2rem;
    }

    .quick-link-card i {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .quick-link-card h3 {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }

    .quick-link-card p {
        font-size: 0.85rem;
    }

    /* Gallery */
    .gallery-item {
        height: 200px;
    }

    .gallery-caption {
        padding: 1rem;
    }

    .gallery-caption h3 {
        font-size: 1rem;
    }

    .gallery-caption p {
        font-size: 0.8rem;
    }

    /* Forms */
    .step {
        width: 28%;
        min-width: 70px;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }

    .step-title {
        font-size: 0.7rem;
    }

    input, select, textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .btn-prev, .btn-next, .btn-submit {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Projects */
    .project-item h2 {
        font-size: 1.4rem;
    }

    .project-item p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Contact form */
    .contact-awt-info,
    .contact-awt-form {
        padding: 2rem 1.5rem;
    }

    .contact-awt-info h2 {
        font-size: 1.5rem;
    }

    /* Footer */
    footer {
        padding: 2rem 1rem;
    }

    .footer-section h3 {
        font-size: 1.1rem;
    }

    .footer-links a {
        font-size: 0.9rem;
    }

    .social-links a {
        width: 35px;
        height: 35px;
    }

    .copyright {
        font-size: 0.75rem;
        line-height: 1.4;
    }
}

/* Extra small devices */
@media (max-width: 320px) {
    .hero h2 {
        font-size: 1.3rem;
    }

    .quick-link-card {
        padding: 1rem;
    }

    .quick-link-card i {
        font-size: 1.8rem;
    }

    .step {
        width: 25%;
        min-width: 60px;
    }

    .step-number {
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .step-title {
        font-size: 0.65rem;
    }

    .project-item h2 {
        font-size: 1.2rem;
    }

    .contact-awt-info,
    .contact-awt-form {
        padding: 1.5rem 1rem;
    }
}
