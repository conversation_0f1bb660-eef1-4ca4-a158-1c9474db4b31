# Access Welfare Trust Scholarship Application Website

This project is a scholarship application website for Access Welfare Trust, featuring a multi-step application form, document upload functionality, and an admin dashboard to review applications.

## Project Structure

```
Trust/
├── index.html                # Homepage
├── application.html          # Multi-step application form
├── confirmation.html         # Submission confirmation page
├── css/
│   └── styles.css            # Main stylesheet
├── js/
│   └── main.js               # JavaScript functionality
├── images/
│   └── awt-logo.png          # Trust logo (to be added)
└── admin/
    ├── login.html            # Admin login page
    └── dashboard.html        # Admin dashboard
```

## Features

### User-facing Features

1. **Homepage**
   - Trust information and logo
   - Application deadline information
   - Link to application form

2. **Multi-step Application Form**
   - Step 1: Personal Details
   - Step 2: Family Details
   - Step 3: Educational Details
   - Step 4: Scholarship Details
   - Step 5: Document Upload & Declaration
   - Form validation
   - Progress tracking
   - Save and continue functionality (using localStorage)

3. **Document Upload**
   - Support for uploading various required documents
   - Photo upload with preview

4. **Confirmation Page**
   - Application summary
   - Unique application ID
   - Print functionality

### Admin Features

1. **Admin Login**
   - Secure login page
   - Authentication (currently using localStorage for demo)

2. **Admin Dashboard**
   - Overview statistics
   - List of applications with filtering and search
   - Application status management (approve/reject)
   - Detailed application view

## Setup Instructions

1. **Prerequisites**
   - Web browser (Chrome, Firefox, Edge, etc.)
   - For production deployment: Web server (Apache, Nginx, etc.)

2. **Local Development**
   - Clone or download this repository
   - Add the Trust logo to the `images` folder
   - Open `index.html` in a web browser

3. **Admin Access (Demo)**
   - Navigate to `/admin/login.html`
   - Use the following credentials:
     - Username: `admin`
     - Password: `admin123`

## Future Enhancements

1. **Backend Integration**
   - Connect to a server-side API for data storage
   - Implement secure authentication
   - Set up email notifications

2. **Advanced Features**
   - Application status tracking for applicants
   - Document verification system
   - Reporting and analytics
   - Export functionality for application data

## Notes

- This is currently a front-end only implementation
- Form data is stored in the browser's localStorage
- For a production environment, a proper backend system should be implemented

## Credits

- Developed for Access Welfare Trust
- Uses Font Awesome for icons
